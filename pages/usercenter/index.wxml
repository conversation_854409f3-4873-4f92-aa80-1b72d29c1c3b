<t-user-center-card userInfo="{{userInfo}}" isPhoneHide="{{true}}" name-class="custom-name-class"
  phone-class="custom-phone-class" avatar-class="customer-avatar-class" currAuthStep="{{currAuthStep}}"
  bind:gotoUserEditPage="gotoUserEditPage" />

<view class="content-wrapper">
  <t-loading theme="dots" size="80rpx" class="loading-wrapper" wx:if="{{pullDownRefreshing}}" />

  <!-- 会员信息区域 -->
  <view class="member-card">
    <view class="member-info">
      <view class="member-icon">
        <!-- <image src="/assets/images/member-icon.png" mode="aspectFit" /> -->
        <t-icon name="member" size="54rpx" prefix="wr" data-name="" data-type="prefix" bind:click="onIconTap" />
      </view>
      <text class="member-level">探店达人</text>
    </view>
    <view class="member-action" bindtap="gotoMyCard">
      <text>我的名片</text>
      <t-icon name="chevron-right" size="40rpx" color="#999" />
    </view>
  </view>

  <!-- 卡券和红包区域 -->
  <view class="coupon-area">
    <view class="coupon-item" bindtap="gotoCoupons">
      <view class="coupon-icon ticket">
        <!-- <t-icon name="coupon" size="40rpx" color="#FF7000" /> -->
        <t-image src="https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/coupons.svg" mode="aspectFit"
          width="40px" height="38rpx" />
      </view>
      <text class="coupon-text">探店券<text class="badge">({{userInfo.remain_coupons}})</text></text>
    </view>
    <view class="divider"></view>
    <view class="coupon-item" bindtap="gotoRedPacket">
      <view class="coupon-icon red-packet">
        <!-- <t-icon name="wallet" size="40rpx" color="#FF4E42" /> -->
        <t-image src="https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/jifen.svg" mode="aspectFit"
          width="40px" height="38rpx" />
      </view>
      <text class="coupon-text">积分<text class="badge">({{userInfo.total_points || 0}})</text></text>
    </view>
  </view>

  <!-- 收益区域 - 精确匹配图片 -->
  <view class="revenue-card">
    <view class="revenue-header">
      <view class="revenue-title">
        <text>我的收益</text>
        <view class="revenue-tag">收益统计包含团长收益</view>
      </view>
      <view class="revenue-detail" bindtap="gotoRevenueDetail">
        <text>明细</text>
        <t-icon name="chevron-right" size="32rpx" color="#999" />
      </view>
    </view>

    <view class="revenue-icons">
      <view class="revenue-icon-item">
        <t-icon name="wallet" size="40rpx" color="#666" />
        <text>可提现余额</text>
      </view>
      <view class="revenue-icon-item" bindtap="refreshRevenue">
        <t-icon name="refresh" size="40rpx" color="#666" />
        <text>刷新</text>
      </view>
      <view class="revenue-icon-item">
        <t-icon name="chart" size="40rpx" color="#666" />
        <text>累计提现</text>
      </view>
    </view>

    <view class="revenue-amounts">
      <text class="revenue-number">{{userInfo.balance}}</text>
      <button class="revenue-button" bindtap="withdrawMoney">提现</button>
      <text class="revenue-number">{{userInfo.total_withdrawals}}</text>
    </view>

    <view class="revenue-links">
      <view class="revenue-link-item" bindtap="gotoTeamRules">
        <text>团长等级<text style="margin-left: 20rpx; color: red; font-size: 24rpx;">(公测中)</text></text>
        <view class="link-right">
          <text>LV{{userInfo.user_level}}</text>
          <t-icon name="chevron-right" size="28rpx" color="#999" />
        </view>
      </view>
      <view class="revenue-link-item" bindtap="gotoReferralRewards">
        <text>团长收益</text>
        <view class="link-right">
          <text>¥ {{totalReferralRewards}}</text>
          <t-icon name="chevron-right" size="28rpx" color="#999" />
        </view>
      </view>
      <view class="revenue-link-item" bindtap="gotoActiveMembers">
        <text>有效团员</text>
        <view class="link-right">
          <text>{{effective_members_count}}</text>
          <t-icon name="chevron-right" size="28rpx" color="#999" />
        </view>
      </view>
      <view class="revenue-link-item" bindtap="gotoInactiveMembers">
        <text>未激活团员</text>
        <view class="link-right">
          <text>{{ineffective_members_count}}</text>
          <t-icon name="chevron-right" size="28rpx" color="#999" />
        </view>
      </view>
    </view>
  </view>

  <!-- 订单区域 -->
  <view class="order-group-wrapper">
    <view class="order-group-header">
      <text class="order-group-title">我的订单</text>
      <view class="order-group-all" bindtap="jumpAllOrder">
        <text>全部订单</text>
        <t-icon name="chevron-right" size="40rpx" color="#999" />
      </view>
    </view>
    <view class="order-content">
      <view class="order-item" bindtap="jumpOrderNav" data-type="10">
        <view class="order-content-box">
          <t-badge count="{{paid_orders_count}}" offset="{{ [0, -2] }}" class="wrapper">
            <t-icon name="verified" size="50rpx" color="#FF4E42" />
            <view class="order-content-t">待核销</view>
          </t-badge>
        </view>
      </view>
      <view class="order-item" bindtap="jumpOrderNav" data-type="20">
        <view class="order-content-box">
          <t-badge count="{{pending_upload_orders_count}}" offset="{{ [0, -2] }}" class="wrapper">
            <t-icon name="upload" size="50rpx" color="#9966FF" />
            <view class="order-content-t">已核销</view>
          </t-badge>
        </view>
      </view>
      <view class="order-item" bindtap="jumpOrderNav" data-type="30">
        <view class="order-content-box">
          <t-badge count="{{under_review_orders_count}}" offset="{{ [0, -2] }}" class="wrapper">
            <t-icon name="time" size="50rpx" color="#FF7000" />
            <view class="order-content-t">审核中</view>
          </t-badge>
        </view>
      </view>
      <view class="order-item" bindtap="jumpOrderNav" data-type="31">
        <view class="order-content-box">
          <t-icon name="check-circle-filled" size="50rpx" color="#4CD964" />
          <view class="order-content-t">已完成</view>
        </view>
      </view>
      <view class="order-item" bindtap="jumpOrderNav" data-type="32">
        <view class="order-content-box">
          <t-badge count="{{audit_failed_orders_count}}" offset="{{ [0, -2] }}" class="wrapper">
            <t-icon name="close-circle" size="50rpx" color="#999999" />
            <view class="order-content-t">已驳回</view>
          </t-badge>
        </view>
      </view>
    </view>
  </view>

  <!-- 宫格区域 -->
  <view class="grids">
    <t-grid column="5" align="center" hover="true" t-class="grids">
      <t-grid-item wx:for="{{gridItems}}" wx:key="key" text="{{item.title}}" icon="{{item.icon}}" data-item="{{item}}"
        bind:click="handleGridItemClick"></t-grid-item>
    </t-grid>
  </view>

  <!-- 额外菜单 -->
  <view wx:for="{{menuData}}" wx:key="item" class="cell-box">
    <t-cell-group>
      <t-cell wx:for="{{item}}" wx:for-item="xitem" wx:for-index="xindex" wx:key="xindex" title="{{xitem.title}}"
        arrow="{{!xitem.icon}}" note="{{xitem.tit}}" data-type="{{xitem.type}}" bordered="{{false}}"
        bind:click="onClickCell" t-class="t-cell-padding" t-class-note="order-group-note"
        t-class-left="order-group__left">
        <t-icon name="{{xitem.icon}}" size="48rpx" slot="note" />
      </t-cell>
    </t-cell-group>
  </view>

  <view class="footer__version">当前版本 {{versionNo}}</view>
</view>

<t-popup visible="{{showMakePhone}}" placement="bottom" bind:visible-change="closeMakePhone" data-index="2">
  <view class="popup-content">
    <view class="popup-title border-bottom-1px" wx:if="{{customerServiceInfo.serviceTimeDuration}}">
      服务时间: {{customerServiceInfo.serviceTimeDuration}}
    </view>
    <!-- <view class="popup-phone {{showKefu ? 'border-bottom-1px' : ''}}" bind:tap="call">电话客服</view> -->
    <button class="popup-phone border-bottom-1px online" open-type="contact" wx:if="{{showKefu}}">在线客服</button>
    <view class="popup-close" bind:tap="closeMakePhone">取消</view>
  </view>
</t-popup>

<video
  id="myVideo"
  src="https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/videos/intro1.mp4"
  controls="{{true}}"
  autoplay="{{false}}"
  show-center-play-btn="{{true}}"
  show-play-btn="{{true}}"
  show-fullscreen-btn="{{true}}"
  enable-play-gesture="{{true}}"
  object-fit="contain"
  bindplay="onVideoPlay"
  bindfullscreenchange="onVideoFullscreenChange"
  bindended="onVideoEnded"
  bindpause="onVideoPause"
  style="width: 1px; height: 1px; position: fixed; top: -1000px; left: -1000px;">
</video>

<t-toast id="t-toast" />
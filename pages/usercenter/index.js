import { fetchUserCenter } from "../../services/usercenter/fetchUsercenter";
import Toast from "tdesign-miniprogram/toast/index";
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { authStore } from "../../stores/authStore";
import { orderStore } from "../../stores/orderStore";
import { userStore } from "../../stores/userStore";

import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
dayjs.extend(isBetween);

const menuData = [
	// [
	//   {
	//     title: '收货地址',
	//     tit: '',
	//     url: '',
	//     type: 'address',
	//   },
	//   {
	//     title: '优惠券',
	//     tit: '',
	//     url: '',
	//     type: 'coupon',
	//   },
	//   {
	//     title: '积分',
	//     tit: '',
	//     url: '',
	//     type: 'point',
	//   },
	// ],
	// [
	// 	{
	// 	  title: '帮助中心',
	// 	  tit: '',
	// 	  url: '',
	// 	  type: 'help-center',
	// 	},
	// 	{
	// 	  title: '常见问题',
	// 	  tit: '',
	// 	  url: '',
	// 	  type: 'help-center',
	// 	},
	// 	{
	// 		title: "客服热线",
	// 		tit: "",
	// 		url: "",
	// 		type: "service",
	// 		icon: "service",
	// 	},
	// ],
	[
		{
			title: "新手指导",
			tit: "",
			url: "https://636c-cloud1-0gpy573m8caa7db3-1321286342.tcb.qcloud.la/app/videos/intro1.mp4",
			type: "guide",
		},
		{
			title: "清除缓存",
			tit: "",
			url: "",
			type: "logout",
		},
	],
];

// const orderTagInfos = [
//   {
//     title: '待付款',
//     iconName: 'wallet',
//     orderNum: 0,
//     tabType: 5,
//     status: 1,
//   },
//   {
//     title: '待发货',
//     iconName: 'deliver',
//     orderNum: 0,
//     tabType: 10,
//     status: 1,
//   },
//   {
//     title: '待收货',
//     iconName: 'package',
//     orderNum: 0,
//     tabType: 40,
//     status: 1,
//   },
//   {
//     title: '待评价',
//     iconName: 'comment',
//     orderNum: 0,
//     tabType: 60,
//     status: 1,
//   },
//   {
//     title: '退款/售后',
//     iconName: 'exchang',
//     orderNum: 0,
//     tabType: 0,
//     status: 1,
//   },
// ];

const orderTagInfos = [
	{
		title: "待核销",
		iconName: "wallet",
		orderNum: 0,
		tabType: 5,
		status: 1,
	},
	{
		title: "待上传",
		iconName: "deliver",
		orderNum: 0,
		tabType: 10,
		status: 1,
	},
	{
		title: "审核中",
		iconName: "package",
		orderNum: 0,
		tabType: 40,
		status: 1,
	},
	{
		title: "已完成",
		iconName: "comment",
		orderNum: 0,
		tabType: 60,
		status: 1,
	},
	{
		title: "已驳回",
		iconName: "exchang",
		orderNum: 0,
		tabType: 0,
		status: 1,
	},
];

const getDefaultData = () => ({
	miniProgram: null,
	showMakePhone: false,
	userInfo: {
		uid: 0,
		avatar: "",
		nickname: "微信用户",
	},
	menuData,
	orderTagInfos,
	customerServiceInfo: {},
	currAuthStep: 2,
	showKefu: true,
	versionNo: "develop",
	isUpdatingStock: false,
	gridItems: [
		{
			title: "我的收藏",
			icon: "bookmark",
			key: "favorites",
		},
		{
			title: "达人入驻",
			icon: "user-checked",
			key: "talent",
		},
		{
			title: "商家合作",
			icon: "cooperate",
			key: "cooperate",
		},
		// {
		// 	title: "意见反馈",
		// 	icon: "questionnaire-double",
		// 	key: "feedback",
		// },
		{
			title: "联系客服",
			icon: "service",
			key: "service",
		},
		{
			title: "邀请好友",
			icon: "share",
			key: "invite",
		},
		// {
		// 	title: "我的团员",
		// 	icon: "app",
		// 	key: "team",
		// },
		// {
		// 	title: "我的收益",
		// 	icon: "app",
		// 	key: "revenueDetail",
		// },
	],
});

Page({
	data: getDefaultData(),

	onLoad() {
		this.getVersionInfo();
		const { miniProgram } = wx.getAccountInfoSync();
		this.setData({
			miniProgram: miniProgram,
		});

		this.storeBindings = createStoreBindings(this, {
			store: authStore,
			fields: ["userInfo"],
			actions: ["refreshUserInfo", "saveAvatar"],
		});
		this.orderStoreBindings = createStoreBindings(this, {
			store: orderStore,
			fields: [
				"paid_orders_count",
				"pending_upload_orders_count",
				"under_review_orders_count",
				"audit_failed_orders_count",
			],
			actions: ["fetchOrderCounts"],
		});
		this.userStoreBindings = createStoreBindings(this, {
			store: userStore,
			fields: [
				"effective_members_count",
				"ineffective_members_count",
				"totalWithdrawedAmount",
				"totalReferralRewards",
			],
			actions: [
				"fetchTeamMemberCounts",
				"fetchTotalWithdrawedAmount",
				"fetchReferralRewards",
			],
		});
		// this.init();
	},

	onUnload() {
		this.storeBindings.destroyStoreBindings();
		this.orderStoreBindings.destroyStoreBindings();
		this.userStoreBindings.destroyStoreBindings();
	},

	onShow() {
		this.getTabBar().init();
		this.handleIsUpdatingStock();
		this.init();
	},

	onPullDownRefresh() {
		console.debug("onPullDownRefresh");
		// this.setData({ enable: true });
		// setTimeout(() => {
		//   this.setData({ enable: false });
		// }, 500);
		this.setData({
			pullDownRefreshing: true,
		});

		this.init();
		wx.stopPullDownRefresh();

		setTimeout(() => {
			this.setData({
				pullDownRefreshing: false,
			});
		}, 1500);
	},

	init() {
		this.refreshUserInfo();
		this.fetchOrderCounts();
		this.fetchTeamMemberCounts();
		// this.fetchTotalWithdrawedAmount(); // 累计提现
		this.fetchReferralRewards(); // 邀请奖励

		// this.fetUseriInfoHandle();

		this.setData({
			currAuthStep: 3,
		});
	},

	fetUseriInfoHandle() {
		fetchUserCenter().then(
			({
				userInfo,
				countsData,
				orderTagInfos: orderInfo,
				customerServiceInfo,
			}) => {
				// eslint-disable-next-line no-unused-expressions
				menuData?.[0].forEach((v) => {
					countsData.forEach((counts) => {
						if (counts.type === v.type) {
							// eslint-disable-next-line no-param-reassign
							v.tit = counts.num;
						}
					});
				});
				const info = orderTagInfos.map((v, index) => ({
					...v,
					...orderInfo[index],
				}));
				this.setData({
					// userInfo,
					menuData,
					orderTagInfos: info,
					customerServiceInfo,
					currAuthStep: 2,
				});
				wx.stopPullDownRefresh();
			},
		);
	},

	onClickCell({ currentTarget }) {
		const { type } = currentTarget.dataset;

		switch (type) {
			case "address": {
				wx.navigateTo({ url: "/pages/usercenter/address/list/index" });
				break;
			}
			case "service": {
				this.openMakePhone();
				break;
			}
			case "help-center": {
				// Toast({
				//   context: this,
				//   selector: '#t-toast',
				//   message: '暂未开放',
				//   icon: '',
				//   duration: 1000,
				// });
				wx.navigateTo({ url: "/pages/faq/index" });
				break;
			}
			case "point": {
				Toast({
					context: this,
					selector: "#t-toast",
					message: "你点击了积分菜单",
					icon: "",
					duration: 1000,
				});
				break;
			}
			case "coupon": {
				wx.navigateTo({ url: "/pages/coupon/coupon-list/index" });
				break;
			}
			case "logout": {
				wx.showModal({
					title: "提示",
					content: "确定要清除缓存吗？",
					success: (res) => {
						if (res.confirm) {
							console.debug("confirm clearStorage");
							// authStore.logout();
							wx.clearStorageSync();
							// this.setData({
							//   currAuthStep: 1,
							//   userInfo: {
							//     uid: 0,
							//     avatar: '',
							//     nickname: '请登录',
							//   },
							// })
							wx.showToast({
								title: "清除成功",
								icon: "success",
								duration: 2000,
							});
							// wx.reLaunch({
							//   url: '/pages/usercenter/index',
							//   success: function () {
							//     authStore.login();
							//   }
							// });
							wx.restartMiniProgram({
								path: "/pages/usercenter/index",
								success: function () {
									console.debug("restartMiniProgram success");
								},
								fail: function (err) {
									console.error(err);
								},
							});
						}
					},
				});
				break;
			}
			case "guide": {
				console.debug("点击新手指导，开始播放视频");
				this.videoCtx = this.videoCtx || wx.createVideoContext('myVideo')

				// 直接请求全屏播放
				this.videoCtx.requestFullScreen({
					direction: 0, // 0度表示竖屏全屏
					success: (res) => {
						console.debug("全屏请求成功，开始播放:", res);
						// 全屏成功后再播放
						setTimeout(() => {
							this.videoCtx.play();
						}, 200);
					},
					fail: (err) => {
						console.error("全屏请求失败:", err);
						// 如果全屏失败，尝试普通播放
						this.videoCtx.play();
					}
				});
				break;
			}
			default: {
				Toast({
					context: this,
					selector: "#t-toast",
					message: "未知跳转",
					icon: "",
					duration: 1000,
				});
				break;
			}
		}
	},

	jumpOrderNav(e) {
		console.debug("e:", e);
		const status = e.currentTarget.dataset.type;
		if (this.data.miniProgram.version) {
			wx.requestSubscribeMessage({
				tmplIds: [
					"hT3QAbcAGuE6__OiRqr3ufdgPNgA8wSTMehzk2KeqRk",
					"DK_oyJnwwg-ulgO9LQnxoUhjDCIblLKHXKnhsCVK31o",
					"iN2AGR_H7lD9q-kGPL54clrDKTNdeCSaGw2NIMWbanc",
				],
				success(res) {
					console.debug("wx.requestSubscribeMessage", res);
				},
			});
		}
		console.debug("status:", status);
		wx.navigateTo({ url: `/pages/order/list/index?status=${status}` });
	},

	jumpAllOrder() {
		if (this.data.miniProgram.version) {
			wx.requestSubscribeMessage({
				tmplIds: [
					"hT3QAbcAGuE6__OiRqr3ufdgPNgA8wSTMehzk2KeqRk",
					"DK_oyJnwwg-ulgO9LQnxoUhjDCIblLKHXKnhsCVK31o",
					"iN2AGR_H7lD9q-kGPL54clrDKTNdeCSaGw2NIMWbanc",
				],
				success(res) {
					console.debug("wx.requestSubscribeMessage", res);
				},
			});
		}
		wx.navigateTo({ url: "/pages/order/list/index" });
	},

	openMakePhone() {
		this.setData({ showMakePhone: true });
	},

	closeMakePhone() {
		this.setData({ showMakePhone: false });
	},

	call() {
		wx.makePhoneCall({
			phoneNumber: this.data.customerServiceInfo.servicePhone,
		});
	},

	gotoUserEditPage() {
		console.debug("gotoUserEditPage");
		// const { currAuthStep } = this.data;
		// if (currAuthStep === 2) {
		//   wx.navigateTo({ url: '/pages/usercenter/person-info/index' });
		// } else {
		//   this.fetUseriInfoHandle();
		// }
		wx.navigateTo({ url: "/pages/usercenter/person-info/index" });
	},

	getVersionInfo() {
		const versionInfo = wx.getAccountInfoSync();
		const { version, envVersion = __wxConfig } = versionInfo.miniProgram;
		this.setData({
			versionNo: envVersion === "release" ? "release " + version : envVersion,
		});
	},

	// 跳转到我的名片
	gotoMyCard() {
		wx.navigateTo({
			url: "/pages/cards/index",
		});
	},

	// 跳转到我的卡券
	gotoCoupons() {
		wx.navigateTo({
			url: "/pages/coupons/index",
		});
	},

	// 跳转到我的积分
	gotoRedPacket() {
		wx.navigateTo({
			url: "/pages/points/index",
		});
	},

	// 刷新收益数据
	async refreshRevenue() {
		await this.refreshUserInfo();
		wx.showToast({
			title: "刷新成功",
			icon: "success",
			duration: 2000,
		});
	},

	// 提现操作
	withdrawMoney() {
		if (this.data.isUpdatingStock) {
			Toast({
				context: this,
				selector: "#t-toast",
				message: "系统正在更新库存，请稍后再提现",
				icon: "",
				duration: 1000,
			});
			return;
		}
		wx.navigateTo({
			url: "/pages/usercenter/withdraw/index",
		});
	},

	// 跳转到收益明细
	gotoRevenueDetail() {
		wx.navigateTo({
			url: "/pages/usercenter/reward/index",
		});
	},

	// 跳转到团长收益
	gotoReferralRewards() {
		wx.navigateTo({
			url: "/pages/usercenter/reward/index",
		});
	},

	// 跳转到团长规则页面
	gotoTeamRules() {
		wx.navigateTo({
			url: "/pages/usercenter/team-rules/index",
		});
	},

	// 跳转到有效团员
	gotoActiveMembers() {
		wx.navigateTo({
			url: "/pages/usercenter/team/index?tab=active",
		});
	},

	// 跳转到未激活团员
	gotoInactiveMembers() {
		wx.navigateTo({
			url: "/pages/usercenter/team/index?tab=inactive",
		});
	},

	handleGridItemClick(e) {
		console.debug(e);
		const { item } = e.currentTarget.dataset;
		switch (item.key) {
			case "favorites": {
				wx.navigateTo({
					url: "/pages/usercenter/favorites/index",
				});
				break;
			}
			case "service": {
				this.openMakePhone();
				break;
			}
			case "invite": {
				wx.switchTab({
					url: "/pages/invite/index",
				});
				break;
			}
			case "cooperate": {
				// Toast({
				// 	context: this,
				// 	selector: "#t-toast",
				// 	message: "敬请期待",
				// 	icon: "",
				// 	duration: 2000,
				// });
				wx.navigateTo({
					url: "/pages/usercenter/cooperate/index",
				});
				break;
			}
			case "feedback": {
				Toast({
					context: this,
					selector: "#t-toast",
					message: "点开右上角胶囊(...)，反馈与投诉，即可提交意见反馈。",
					icon: "",
					duration: 2000,
				});
				// Toast({
				// 	context: this,
				// 	selector: "#t-toast",
				// 	message: "请不要轻易投诉，任何问题都可以找我们客服解决。",
				// 	icon: "",
				// 	duration: 2000,
				// });
				break;
			}
			case "talent": {
				wx.navigateTo({
					url: "/pages/cards/index",
				});
				break;
			}
			default: {
				break;
			}
		}
	},

	// 判断是否在更新时间区间内
	handleIsUpdatingStock() {
		// 定义时间区间
		const startTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:00:00`;
		const endTimeStr = `${dayjs().format("YYYY-MM-DD")} 00:11:00`;
		const startTime = dayjs(startTimeStr, "YYYY-MM-DD HH:mm:ss");
		const endTime = dayjs(endTimeStr, "YYYY-MM-DD HH:mm:ss");

		// 要检查的时间
		const checkTime = dayjs();
		// 判断是否在区间内（包含边界）
		const between = checkTime.isBetween(startTime, endTime, "second", "[]");

		if (between) {
			this.setData({
				isUpdatingStock: true,
			});
		} else {
			this.setData({
				isUpdatingStock: false,
			});
		}
	},

	// 视频播放事件处理
	onVideoPlay() {
		console.debug("新手指导视频开始播放");
	},

	// 视频全屏状态变化事件
	onVideoFullscreenChange(e) {
		console.debug("视频全屏状态变化:", e.detail);
		const { fullScreen } = e.detail;

		// 如果退出全屏（用户主动最小化），则停止播放并重置
		if (!fullScreen) {
			console.debug("用户退出全屏，停止播放");
			const videoContext = wx.createVideoContext('myVideo', this);
			videoContext.pause();
			videoContext.seek(0); // 重置到开始位置
		}
	},

	// 视频播放结束事件
	onVideoEnded() {
		console.debug("新手指导视频播放结束");
	},

	// 视频暂停事件
	onVideoPause() {
		console.debug("新手指导视频暂停");
	},
});
